import 'package:get/get.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/common/utils/error_handler.dart';
import 'package:rolio/common/constants/error_codes.dart';
import 'package:rolio/modules/wallet/service/wallet_service.dart';
import '../model/card.dart';
import '../model/card_rarity.dart';
import '../model/gacha_result.dart';
import '../repository/card_repository.dart';

/// Card service
class CardService extends GetxService {
  /// Card repository
  final ICardRepository _repository;
  
  /// Wallet service
  final WalletService _walletService = Get.find<WalletService>();
  
  /// User's owned cards
  final RxList<Card> userCards = <Card>[].obs;
  
  /// Available cards for gacha
  final RxList<Card> availableCards = <Card>[].obs;
  
  /// Loading state
  final RxBool isLoading = false.obs;
  
  /// Gacha costs
  static const int singlePullCost = 100;
  static const int tenPullCost = 1000;
  
  CardService(this._repository);

  @override
  void onInit() {
    super.onInit();
    LogUtil.debug('CardService: 初始化');
    
    // Load initial data
    _loadInitialData();
  }

  /// Load initial data
  Future<void> _loadInitialData() async {
    await refreshUserCards();
    await refreshAvailableCards();
  }

  /// Refresh user's owned cards
  Future<bool> refreshUserCards() async {
    try {
      isLoading.value = true;
      LogUtil.debug('CardService: 刷新用户卡片');
      
      final cards = await _repository.getUserCards();
      userCards.assignAll(cards);
      
      LogUtil.debug('CardService: 用户卡片刷新成功，数量: ${cards.length}');
      return true;
    } catch (e) {
      LogUtil.error('CardService: 刷新用户卡片失败: $e');
      ErrorHandler.handleException(
        AppException('获取卡片失败', originalError: e, code: ErrorCodes.BUSINESS_ERROR),
      );
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// Refresh available cards
  Future<bool> refreshAvailableCards() async {
    try {
      LogUtil.debug('CardService: 刷新可用卡片');
      
      final cards = await _repository.getAvailableCards();
      availableCards.assignAll(cards);
      
      LogUtil.debug('CardService: 可用卡片刷新成功，数量: ${cards.length}');
      return true;
    } catch (e) {
      LogUtil.error('CardService: 刷新可用卡片失败: $e');
      ErrorHandler.handleException(
        AppException('获取可用卡片失败', originalError: e, code: ErrorCodes.BUSINESS_ERROR),
      );
      return false;
    }
  }

  /// Perform single gacha pull
  Future<GachaResult?> performSinglePull() async {
    return await _performGacha('single', singlePullCost, 1);
  }

  /// Perform ten gacha pulls
  Future<GachaResult?> performTenPull() async {
    return await _performGacha('ten', tenPullCost, 10);
  }

  /// Perform gacha pull
  Future<GachaResult?> _performGacha(String pullType, int cost, int count) async {
    try {
      isLoading.value = true;
      LogUtil.debug('CardService: 执行抽卡，类型: $pullType，费用: $cost');
      
      // Check if user has enough diamonds
      if (!_walletService.hasEnoughDiamonds(cost)) {
        LogUtil.warn('CardService: 钻石不足，需要: $cost，当前: ${_walletService.currentBalance}');
        ErrorHandler.handleException(
          AppException('钻石不足', code: ErrorCodes.BUSINESS_ERROR),
        );
        return null;
      }
      
      // Perform gacha
      final result = await _repository.performGacha(
        pullType: pullType,
        cost: cost,
        count: count,
      );
      
      // Deduct diamonds from wallet
      final spendSuccess = await _walletService.spendDiamonds(cost, '抽卡消费 ($pullType)');
      if (!spendSuccess) {
        LogUtil.error('CardService: 扣除钻石失败');
        ErrorHandler.handleException(
          AppException('扣费失败', code: ErrorCodes.BUSINESS_ERROR),
        );
        return null;
      }
      
      // Update result with remaining balance
      final updatedResult = GachaResult(
        cards: result.cards,
        totalCost: result.totalCost,
        remainingBalance: _walletService.currentBalance,
        timestamp: result.timestamp,
        pullType: result.pullType,
        metadata: result.metadata,
      );
      
      // Refresh user cards
      await refreshUserCards();
      
      LogUtil.info('CardService: 抽卡成功，获得卡片: ${result.cards.length}，剩余钻石: ${_walletService.currentBalance}');
      return updatedResult;
    } catch (e) {
      LogUtil.error('CardService: 抽卡失败: $e');
      ErrorHandler.handleException(
        AppException('抽卡失败', originalError: e, code: ErrorCodes.BUSINESS_ERROR),
      );
      return null;
    } finally {
      isLoading.value = false;
    }
  }

  /// Check if user can afford single pull
  bool get canAffordSinglePull => _walletService.hasEnoughDiamonds(singlePullCost);
  
  /// Check if user can afford ten pull
  bool get canAffordTenPull => _walletService.hasEnoughDiamonds(tenPullCost);
  
  /// Get current wallet balance
  int get currentBalance => _walletService.currentBalance;
  
  /// Get cards by rarity
  List<Card> getCardsByRarity(CardRarity rarity) {
    return userCards.where((card) => card.rarity == rarity).toList();
  }
  
  /// Get total card count (including duplicates)
  int get totalCardCount => userCards.fold(0, (total, card) => total + card.ownedCount);
  
  /// Get unique card count
  int get uniqueCardCount {
    final uniqueIds = userCards.map((card) => card.id).toSet();
    return uniqueIds.length;
  }
  
  /// Clear user card collection
  Future<bool> clearUserCards() async {
    try {
      LogUtil.debug('CardService: 清空用户卡片收藏');

      final success = await _repository.clearUserCards();
      if (success) {
        userCards.clear();
        LogUtil.info('CardService: 用户卡片收藏清空成功');
      } else {
        LogUtil.error('CardService: 用户卡片收藏清空失败');
      }

      return success;
    } catch (e) {
      LogUtil.error('CardService: 清空用户卡片收藏异常: $e');
      ErrorHandler.handleException(
        AppException('清空卡片收藏失败', originalError: e, code: ErrorCodes.BUSINESS_ERROR),
      );
      return false;
    }
  }

  /// Create test data for development
  Future<bool> createTestData() async {
    try {
      LogUtil.debug('CardService: 创建测试数据');

      // Add some diamonds to wallet for testing
      final addDiamondsSuccess = await _walletService.addDiamonds(5000, '测试数据创建');
      if (!addDiamondsSuccess) {
        LogUtil.warn('CardService: 添加测试钻石失败');
      }

      // Refresh data
      await refreshUserCards();
      await refreshAvailableCards();

      LogUtil.info('CardService: 测试数据创建成功');
      return true;
    } catch (e) {
      LogUtil.error('CardService: 创建测试数据失败: $e');
      ErrorHandler.handleException(
        AppException('创建测试数据失败', originalError: e, code: ErrorCodes.BUSINESS_ERROR),
      );
      return false;
    }
  }
}
